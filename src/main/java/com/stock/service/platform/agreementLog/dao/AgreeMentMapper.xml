<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.agreementLog.dao.AgreementMapper">

    <select id="getAgreement" resultType="com.stock.service.platform.agreementLog.dto.AgreementDto">
        (
            SELECT
                id AS id,
                agreement_content AS agreementContent,
                agreement_type AS agreementType,
                version AS version
            FROM `capital_platform_jt`.sa_agreement
            WHERE agreement_type = '1'
            ORDER BY create_time DESC
                LIMIT 1
        )
        UNION ALL
        (
            SELECT
                id AS id,
                agreement_content AS agreementContent,
                agreement_type AS agreementType,
                version AS version
            FROM `capital_platform_jt`.sa_agreement
            WHERE agreement_type = '2'
            ORDER BY create_time DESC
                LIMIT 1
        )
    </select>

    <insert id="saveLoginAgreementLogBatch" parameterType="list">
        INSERT INTO `capital_platform_jt`.sa_login_agreement_log (
            id,
            agreement_id,
            user_id,
            user_name,
            login_ip,
            create_user,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            uuid_short(),
            #{item.agreementId},
            #{item.userId},
            #{item.userName},
            #{item.loginIp},
            #{item.createUser},
            #{item.createTime}
            )
        </foreach>
    </insert>

</mapper>