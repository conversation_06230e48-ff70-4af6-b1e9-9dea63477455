package com.stock.service.platform.common.entity;

import java.io.Serializable;
import java.util.Date;

public class ChatFeedback implements Serializable {
    private String id;

    private String question;

    private String answer;

    private String feedbackType;

    private String feedbackName;

    private String telephone;

    private String feedbackContent;

    private String newReply;

    private Date newReplyTime;

    private String isReply;

    private String isReplyNew;

    private String userName;

    private String replyName;

    private String isFeedbackNew;

    private String newFeedback;

    private Date newFeedbackTime;

    private String status;

    private Date createTime;

    private String createUser;

    private Date updateTime;

    private String updateUser;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question == null ? null : question.trim();
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer == null ? null : answer.trim();
    }

    public String getFeedbackType() {
        return feedbackType;
    }

    public void setFeedbackType(String feedbackType) {
        this.feedbackType = feedbackType == null ? null : feedbackType.trim();
    }

    public String getFeedbackName() {
        return feedbackName;
    }

    public void setFeedbackName(String feedbackName) {
        this.feedbackName = feedbackName == null ? null : feedbackName.trim();
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone == null ? null : telephone.trim();
    }

    public String getFeedbackContent() {
        return feedbackContent;
    }

    public void setFeedbackContent(String feedbackContent) {
        this.feedbackContent = feedbackContent == null ? null : feedbackContent.trim();
    }

    public String getNewReply() {
        return newReply;
    }

    public void setNewReply(String newReply) {
        this.newReply = newReply == null ? null : newReply.trim();
    }

    public Date getNewReplyTime() {
        return newReplyTime;
    }

    public void setNewReplyTime(Date newReplyTime) {
        this.newReplyTime = newReplyTime;
    }

    public String getIsReply() {
        return isReply;
    }

    public void setIsReply(String isReply) {
        this.isReply = isReply == null ? null : isReply.trim();
    }

    public String getIsReplyNew() {
        return isReplyNew;
    }

    public void setIsReplyNew(String isReplyNew) {
        this.isReplyNew = isReplyNew == null ? null : isReplyNew.trim();
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public String getReplyName() {
        return replyName;
    }

    public void setReplyName(String replyName) {
        this.replyName = replyName == null ? null : replyName.trim();
    }

    public String getIsFeedbackNew() {
        return isFeedbackNew;
    }

    public void setIsFeedbackNew(String isFeedbackNew) {
        this.isFeedbackNew = isFeedbackNew == null ? null : isFeedbackNew.trim();
    }

    public String getNewFeedback() {
        return newFeedback;
    }

    public void setNewFeedback(String newFeedback) {
        this.newFeedback = newFeedback == null ? null : newFeedback.trim();
    }

    public Date getNewFeedbackTime() {
        return newFeedbackTime;
    }

    public void setNewFeedbackTime(Date newFeedbackTime) {
        this.newFeedbackTime = newFeedbackTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser == null ? null : updateUser.trim();
    }
}
