package com.stock.service.platform.common.entity;

import java.io.Serializable;
import java.util.Date;

public class ChatFeedbackConversationRecord implements Serializable {
    private String id;

    private String feedbackId;

    private String content;

    private String realName;

    private String replyFeedbackStatus;

    private Date createTime;

    private String createUser;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getFeedbackId() {
        return feedbackId;
    }

    public void setFeedbackId(String feedbackId) {
        this.feedbackId = feedbackId == null ? null : feedbackId.trim();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName == null ? null : realName.trim();
    }

    public String getReplyFeedbackStatus() {
        return replyFeedbackStatus;
    }

    public void setReplyFeedbackStatus(String replyFeedbackStatus) {
        this.replyFeedbackStatus = replyFeedbackStatus == null ? null : replyFeedbackStatus.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser == null ? null : createUser.trim();
    }
}
