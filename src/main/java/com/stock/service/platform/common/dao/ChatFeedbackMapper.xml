<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.stock.service.platform.common.dao.ChatFeedbackMapper">
  <resultMap id="BaseResultMap" type="com.stock.service.platform.compliance.dto.ChatFeedbackDto">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="chat_content_id" jdbcType="VARCHAR" property="chatContentId" />
    <result column="question" jdbcType="LONGVARCHAR" property="question" />
    <result column="answer" jdbcType="LONGVARCHAR" property="answer" />
    <result column="feedback_type" jdbcType="VARCHAR" property="feedbackType" />
    <result column="feedback_name" jdbcType="VARCHAR" property="feedbackName" />
    <result column="telephone" jdbcType="VARCHAR" property="telephone" />
    <result column="feedback_content" jdbcType="LONGVARCHAR" property="feedbackContent" />
    <result column="new_reply" jdbcType="LONGVARCHAR" property="newReply" />
    <result column="new_reply_time" jdbcType="TIMESTAMP" property="newReplyTime" />
    <result column="is_reply" jdbcType="VARCHAR" property="isReply" />
    <result column="is_reply_new" jdbcType="VARCHAR" property="isReplyNew" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="reply_name" jdbcType="VARCHAR" property="replyName" />
    <result column="is_feedback_new" jdbcType="VARCHAR" property="isFeedbackNew" />
    <result column="new_feedback" jdbcType="LONGVARCHAR" property="newFeedback" />
    <result column="new_feedback_time" jdbcType="TIMESTAMP" property="newFeedbackTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <insert id="insertSelective" parameterType="com.stock.service.platform.compliance.dto.ChatFeedbackDto">
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.String">
      SELECT CONCAT(uuid_short(),'')
    </selectKey>
    insert into chat_feedback
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="chatContentId != null">
        chat_content_id,
      </if>
      <if test="question != null">
        question,
      </if>
      <if test="answer != null">
        answer,
      </if>
      <if test="feedbackType != null">
        feedback_type,
      </if>
      <if test="feedbackName != null">
        feedback_name,
      </if>
      <if test="telephone != null">
        telephone,
      </if>
      <if test="feedbackContent != null">
        feedback_content,
      </if>
      <if test="newReply != null">
        new_reply,
      </if>
      <if test="newReplyTime != null">
        new_reply_time,
      </if>
      <if test="isReply != null">
        is_reply,
      </if>
      <if test="isReplyNew != null">
        is_reply_new,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="replyName != null">
        reply_name,
      </if>
      <if test="isFeedbackNew != null">
        is_feedback_new,
      </if>
      <if test="newFeedback != null">
        new_feedback,
      </if>
      <if test="newFeedbackTime != null">
        new_feedback_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="chatContentId != null">
        #{chatContentId,jdbcType=VARCHAR},
      </if>
      <if test="question != null">
        #{question,jdbcType=LONGVARCHAR},
      </if>
      <if test="answer != null">
        #{answer,jdbcType=LONGVARCHAR},
      </if>
      <if test="feedbackType != null">
        #{feedbackType,jdbcType=VARCHAR},
      </if>
      <if test="feedbackName != null">
        #{feedbackName,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="feedbackContent != null">
        #{feedbackContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="newReply != null">
        #{newReply,jdbcType=LONGVARCHAR},
      </if>
      <if test="newReplyTime != null">
        #{newReplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isReply != null">
        #{isReply,jdbcType=VARCHAR},
      </if>
      <if test="isReplyNew != null">
        #{isReplyNew,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="replyName != null">
        #{replyName,jdbcType=VARCHAR},
      </if>
      <if test="isFeedbackNew != null">
        #{isFeedbackNew,jdbcType=VARCHAR},
      </if>
      <if test="newFeedback != null">
        #{newFeedback,jdbcType=LONGVARCHAR},
      </if>
      <if test="newFeedbackTime != null">
        #{newFeedbackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="deleteByChatContentId" parameterType="java.lang.String">
    update chat_feedback set status = '0' where chat_content_id = #{chatContentId} and status = '1'
  </update>

  <update id="updateIsReplyNewById" parameterType="java.lang.String">
    update chat_feedback set is_reply_new = '0' where id = #{id} and is_reply_new = '1'
  </update>

  <select id="getFeedbackReplyList" parameterType="com.stock.service.platform.compliance.dto.ChatFeedbackDto"
          resultMap="BaseResultMap">
    SELECT
      cf.id,
      cf.chat_content_id,
      cf.question,
      cf.answer,
      cf.feedback_type,
      cf.feedback_name,
      cf.telephone,
      cf.feedback_content,
      cf.new_reply,
      cf.new_reply_time,
      cf.is_reply,
      cf.is_reply_new,
      cf.user_name,
      cf.reply_name,
      cf.is_feedback_new,
      cf.new_feedback,
      cf.new_feedback_time,
      cf.status,
      cf.create_time,
      cf.create_user,
      cf.update_time,
      cf.update_user
    FROM chat_feedback cf
    WHERE cf.user_name = #{userName}
    AND cf.status = '1'
    <if test="question != null and question != '' and answer != null and answer != ''">
      AND (cf.question LIKE CONCAT('%', #{question}, '%') OR cf.answer LIKE CONCAT('%', #{answer}, '%'))
    </if>
    <if test="feedbackContent != null and feedbackContent != ''">
      AND cf.feedback_content LIKE CONCAT('%', #{feedbackContent}, '%')
    </if>
    <if test="feedbackTypeList != null and feedbackTypeList.size > 0">
      AND cf.feedback_type IN
      <foreach collection="feedbackTypeList" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="startCreateTime != null">
      AND cf.create_time >= #{startCreateTime}
    </if>
    <if test="endCreateTime != null">
      AND cf.create_time &lt;= #{endCreateTime}
    </if>
    ORDER BY cf.is_reply_new DESC, cf.new_reply_time DESC, cf.create_time DESC
    LIMIT #{startRow}, #{pageSize}
  </select>

  <select id="getFeedbackReplyCount" parameterType="com.stock.service.platform.compliance.dto.ChatFeedbackDto"
          resultType="java.lang.Integer">
    SELECT COUNT(1)
    FROM chat_feedback cf
    WHERE cf.user_name = #{userName}
      AND cf.status = '1'
    <if test="question != null and question != '' and answer != null and answer != ''">
      AND (cf.question LIKE CONCAT('%', #{question}, '%') OR cf.answer LIKE CONCAT('%', #{answer}, '%'))
    </if>
    <if test="feedbackContent != null and feedbackContent != ''">
      AND cf.feedback_content LIKE CONCAT('%', #{feedbackContent}, '%')
    </if>
    <if test="feedbackTypeList != null and feedbackTypeList.size > 0">
      AND cf.feedback_type IN
      <foreach collection="feedbackTypeList" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="startCreateTime != null">
      AND cf.create_time >= #{startCreateTime}
    </if>
    <if test="endCreateTime != null">
      AND cf.create_time &lt;= #{endCreateTime}
    </if>
  </select>

  <update id="updateContinueFeedback" parameterType="com.stock.service.platform.compliance.dto.ChatFeedbackDto">
    UPDATE chat_feedback
    <set>
      <if test="newReply != null">
        new_reply = #{newReply,jdbcType=LONGVARCHAR},
      </if>
      <if test="newReplyTime != null">
        new_reply_time = #{newReplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isReply != null">
        is_reply = #{isReply,jdbcType=VARCHAR},
      </if>
      <if test="isReplyNew != null">
        is_reply_new = #{isReplyNew,jdbcType=VARCHAR},
      </if>
      <if test="replyName != null">
        reply_name = #{replyName,jdbcType=VARCHAR},
      </if>
      <if test="isFeedbackNew != null">
        is_feedback_new = #{isFeedbackNew,jdbcType=VARCHAR},
      </if>
      <if test="newFeedback != null">
        new_feedback = #{newFeedback,jdbcType=LONGVARCHAR},
      </if>
      <if test="newFeedbackTime != null">
        new_feedback_time = #{newFeedbackTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
    </set>
    WHERE id = #{id,jdbcType=VARCHAR}
  </update>
    <select id="getTableList" resultMap="BaseResultMap" parameterType="com.stock.service.platform.compliance.dto.ChatFeedbackQueryDto">
        select
            *
        from chat_feedback
        <where>
                `status` = '1'
            <if test="questionKeyword != null and questionKeyword != ''">
                and question like concat('%',#{questionKeyword},'%')
            </if>
            <if test="answerKeyword != null and answerKeyword != ''">
                and answer like concat('%',#{answerKeyword},'%')
            </if>
            <if test="startTime != null and endTime != null">
                and create_time between #{startTime} and DATE(#{endTime}) + INTERVAL 1 DAY - INTERVAL 1 SECOND
            </if>
            <if test="feedbackType != null and feedbackType != ''">
                and feedback_type = #{feedbackType}
            </if>
            <if test="feedbackName != null and feedbackName != ''">
                and feedback_name like concat('%',#{feedbackName},'%')
            </if>
            <if test="replyName != null and replyName != ''">
                and reply_name like concat('%',#{replyName},'%')
            </if>
        </where>
        ORDER BY is_feedback_new DESC, new_feedback_time DESC, create_time DESC
        <if test="startRow != null and pageSize != null">
            limit #{startRow},#{pageSize}
        </if>
    </select>

    <select id="getTotalSize" resultType="java.lang.Integer" parameterType="com.stock.service.platform.compliance.dto.ChatFeedbackQueryDto">
        select
            count(*)
        from chat_feedback
        <where>
                `status` = '1'
            <if test="questionKeyword != null and questionKeyword != ''">
                and question like concat('%',#{questionKeyword},'%')
            </if>
            <if test="answerKeyword != null and answerKeyword != ''">
                and answer like concat('%',#{answerKeyword},'%')
            </if>
            <if test="startTime != null and endTime != null">
                and create_time between #{startTime} and DATE(#{endTime}) + INTERVAL 1 DAY - INTERVAL 1 SECOND
            </if>
            <if test="feedbackType != null and feedbackType != ''">
                and feedback_type = #{feedbackType}
            </if>
            <if test="feedbackName != null and feedbackName != ''">
                and feedback_name like concat('%',#{feedbackName},'%')
            </if>
            <if test="replyName != null and replyName != ''">
                and reply_name like concat('%',#{replyName},'%')
            </if>
        </where>
    </select>

    <select id="getExportList" resultMap="BaseResultMap" parameterType="com.stock.service.platform.compliance.dto.ChatFeedbackQueryDto">
        select
            *
        from chat_feedback
        <where>
                `status` = '1'
            <if test="questionKeyword != null and questionKeyword != ''">
                and question like concat('%',#{questionKeyword},'%')
            </if>
            <if test="answerKeyword != null and answerKeyword != ''">
                and answer like concat('%',#{answerKeyword},'%')
            </if>
            <if test="startTime != null and endTime != null">
                and create_time between #{startTime} and DATE(#{endTime}) + INTERVAL 1 DAY - INTERVAL 1 SECOND
            </if>
            <if test="feedbackType != null and feedbackType != ''">
                and feedback_type = #{feedbackType}
            </if>
            <if test="feedbackName != null and feedbackName != ''">
                and feedback_name like concat('%',#{feedbackName},'%')
            </if>
            <if test="replyName != null and replyName != ''">
                and reply_name like concat('%',#{replyName},'%')
            </if>
        </where>
    </select>
</mapper>
