package com.stock.service.platform.compliance.dto;

import com.stock.core.dto.QueryInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 聊天反馈查询DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ChatFeedbackQueryDto extends QueryInfo<ChatFeedbackQueryDto> implements Serializable {

    /**
     * 问题关键字
     */
    private String questionKeyword;

    /**
     * 回复关键字
     */
    private String answerKeyword;

    /**
     * 反馈类型(回答有误:1    响应慢:2    案例有误:3    法规有误:4    其他:0)
     */
    private String feedbackType;

    /**
     * 用户姓名
     */
    private String feedbackName;

    /**
     * 答复人
     */
    private String replyName;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private static final long serialVersionUID = 1L;
}
