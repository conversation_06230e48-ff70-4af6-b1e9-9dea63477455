'use strict'

import request from '@/utils/request'

// 隐私,协议
export function _getAgreement () {
    return request({
        url: 'agreement/getAgreement',
        method: 'post',
        hiddenLoading: false
    })
}

// 隐私,协议日志
export function _saveLoginAgreementLog (param) {
    return request({
        url: 'agreement/saveLoginAgreementLog',
        method: 'post',
        data: param,
        hiddenLoading: false
    })
}
