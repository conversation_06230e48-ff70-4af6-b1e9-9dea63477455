'use strict'

import request from '@/utils/request'

export function _getTableList(params) {
    return request({
        url: "/feedbackReply/getTableList",
        method: "post",
        data: params,
    });
}

export function _getDropDown(params) {
    return request({
        url: "/feedbackReply/getDropDown",
        method: "post",
        data: params,
    });
}

export function _exportFieldList(params) {
    return request({
        url: "/feedbackReply/exportFieldList",
        method: "post",
        data: params,
        responseType: 'blob'// 设置response类型，下载
    });
}

export function _replyFeedback(params) {
    return request({
        url: "/feedbackReply/replyFeedback",
        method: "post",
        data: params,
    });
}