'use strict'

import request from '@/utils/request'

export function queryPagingLogManageBySelective (query) {
  return request({
    url: '/logManage/queryPagingLogManageBySelective?length=' + query.pageSize + '&start=' + (query.startRow-1)*query.pageSize, // 根据条件分页查询日志
    method: 'post',
    data: query
  })
}

export function queryLoginLogManageBySelective (query) {
  return request({
    url: '/logManage/queryLoginLogManageBySelective?length=' + query.pageSize + '&start=' + (query.startRow-1)*query.pageSize, // 根据条件分页查询日志
    method: 'post',
    data: query
  })
}

export function queryOperationLogManageBySelective (query) {
  return request({
    url: '/logManage/queryOperationLogManageBySelective?length=' + query.pageSize + '&start=' + (query.startRow-1)*query.pageSize, // 根据条件分页查询日志
    method: 'post',
    data: query
  })
}

export function exportExcel (query) {
  return request({
    url: '/logManage/exportExcel',
    method: 'post',
    responseType: 'blob',
    data: query
  })
}

export function loginExportExcel (query) {
  return request({
    url: '/logManage/loginExportExcel',
    method: 'post',
    responseType: 'blob',
    data: query
  })
}

export function operationLogExportExcel (query) {
  return request({
    url: '/logManage/operationLogExportExcel',
    method: 'post',
    responseType: 'blob',
    data: query
  })
}