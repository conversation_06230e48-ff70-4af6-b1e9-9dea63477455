<template>
  <div class="container" style="margin: 0 10px;padding: 20px">
    <div class="log-manage-div" style="">
      <el-tabs v-model="tabModel" @tab-click="handleClick">
        <el-tab-pane label="行为日志" name="behaviorLogs">
          <el-row :gutter="10" class="row-form-list">
            <el-col :span="6">
              <el-input size="small" v-model="queryInfo.companyCode" placeholder="公司编码"></el-input>
            </el-col>
            <el-col :span="6">
              <el-input size="small" v-model="queryInfo.userName" placeholder="操作账号"></el-input>
            </el-col>
            <el-col :span="6">
              <el-input size="small" v-model="queryInfo.logURL" placeholder="方法路径"></el-input>
            </el-col>
            <el-col :span="6">
              <el-date-picker type="date" size='small' v-model="queryInfo.startTime" placeholder="开始时间"
                              format="yyyy-MM-dd" value-format="yyyy-MM-dd" prefix-icon="el-icon-date"
                              style="width: 100%"
              ></el-date-picker>
            </el-col>
          </el-row>
          <el-row :gutter="10" style="margin-top: 10px" class="row-form-list">
            <el-col :span="6">
              <el-date-picker type="date" size='small' v-model="queryInfo.endTime" placeholder="结束时间"
                              format="yyyy-MM-dd" value-format="yyyy-MM-dd" prefix-icon="el-icon-date"
                              style="width: 100%"
              ></el-date-picker>
            </el-col>
            <el-col :span="6" :offset="12" class="text-right">
              <el-button size="mini" type="primary" @click="search" round>查询</el-button>
              <el-button size="mini" @click="clear" round>重置</el-button>
              <el-button @click="exportDetail()" size="mini" round>导出</el-button>
            </el-col>
          </el-row>
          <div style="margin-top: 10px">
            <el-table
                :data="tableData"
                border
                style="width: 100%;margin-bottom: 20px;color: #000"
                :font-size="14"
                header-row-class-name	="custom-header-class-list"
            >
              <el-table-column
                  type="index"
                  width="60px"
                  align="center"
                  label="序号">
              </el-table-column>
              <el-table-column
                  label="公司编码"
                  align="center" width="100px">
                <template slot-scope="scope">
                  <span>{{scope.row.companyCode}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="公司名称"
                  align="center" width="160px">
                <template slot-scope="scope">
                  <span>{{scope.row.companyName}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="操作账号"
                  align="center" width="160px">
                <template slot-scope="scope">
                  <span>{{scope.row.userName}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="操作ip"
                  align="center" width="160px">
                <template slot-scope="scope">
                  <span>{{scope.row.logIP}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="方法路径"
                  align="center" width="200px">
                <template slot-scope="scope">
                  <span>{{scope.row.logURL}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="方法类型"
                  align="center" width="100px">
                <template slot-scope="scope">
                  <span>{{scope.row.logType}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="方法名"
                  align="center" width="200px">
                <template slot-scope="scope">
                  <span>{{scope.row.methodName}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="操作时间"
                  align="center" width="200px">
                <template slot-scope="scope">
                  <span>{{scope.row.createTime}}</span>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
                style="text-align: center;"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="queryInfo.startRow"
                :page-sizes="[10, 20, 50, 200]"
                :page-size="queryInfo.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total">
            </el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="登录日志" name="loginLogs">
          <el-row :gutter="10" class="row-form-list">
            <el-col :span="6">
              <el-input size="small" v-model="loginQueryInfo.userName" placeholder="操作人姓名"></el-input>
            </el-col>
            <el-col :span="6">
              <el-select
                  v-model="loginQueryInfo.logName"
                  placeholder="操作类型"
                  style="width: 100%"
                  clearable>
                <el-option label="用户登录" value="用户登录"></el-option>
                <el-option label="用户登出" value="用户登出"></el-option>
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-date-picker
                  style="width: 100%"
                  value-format="yyyy-MM-dd"
                  v-model="loginQueryInfo.formLogTime"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
              </el-date-picker>
            </el-col>
            <el-col :span="6" style="text-align: right;">
              <el-button size="mini" type="primary" @click="getLoginRecord" round>查询</el-button>
              <el-button size="mini" @click="loginLogClear" round>重置</el-button>
              <el-button @click="loginLogExportDetail()" size="mini" round>导出</el-button>
            </el-col>
          </el-row>
          <div style="margin-top: 10px">
            <el-table
                :data="loginTableData"
                border
                style="width: 100%;margin-bottom: 20px;color: #000"
                :font-size="14"
                header-row-class-name	="custom-header-class-list"
            >
              <el-table-column
                  type="index"
                  width="60px"
                  align="center"
                  label="序号">
              </el-table-column>
              <el-table-column
                  label="操作人姓名"
                  align="center">
                <template slot-scope="scope">
                  <span>{{scope.row.userName}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="操作类型"
                  align="center">
                <template slot-scope="scope">
                  <span>{{scope.row.logName}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="操作IP"
                  align="center">
                <template slot-scope="scope">
                  <span>{{scope.row.logIp}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="操作时间"
                  align="center" >
                <template slot-scope="scope">
                  <span>{{scope.row.logTime}}</span>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
                style="text-align: center;"
                @size-change="handleSizeChange1"
                @current-change="handleCurrentChange1"
                :current-page="loginQueryInfo.startRow"
                :page-sizes="[10, 20, 50, 200]"
                :page-size="loginQueryInfo.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="loginTotal">
            </el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="后台行为日志" name="operationLogs">
          <el-row :gutter="10" class="row-form-list">
            <el-col :span="6">
              <el-input size="small" v-model="operationQueryInfo.userName" placeholder="操作账号"></el-input>
            </el-col>
            <el-col :span="6">
              <el-select size="small" class="full" clearable filterable placeholder="所属机构"
                         :show-overflow-tooltip="true" v-model="operationQueryInfo.orgId">
                <el-option
                    :key="'org' + index"
                    :label="item.orgName"
                    :value="item.orgId"
                    v-for="(item, index) in orgList"
                ></el-option>
              </el-select>
            </el-col>
<!--            <el-col :span="6">-->
<!--              <el-select size="small" class="full" clearable filterable placeholder="所属上市公司"-->
<!--                         v-model="operationQueryInfo.companyId">-->
<!--                <el-option-->
<!--                    :key="'company' + index"-->
<!--                    :label="item.orgName"-->
<!--                    :value="item.orgId"-->
<!--                    v-for="(item, index) in companyList"-->
<!--                ></el-option>-->
<!--              </el-select>-->
<!--            </el-col>-->
            <el-col :span="6">
              <el-date-picker type="date" size='small' v-model="operationQueryInfo.startTime" placeholder="开始时间"
                              format="yyyy-MM-dd" value-format="yyyy-MM-dd" prefix-icon="el-icon-date"
                              style="width: 100%"
              ></el-date-picker>
            </el-col>
            <el-col :span="6">
              <el-date-picker type="date" size='small' v-model="operationQueryInfo.endTime" placeholder="结束时间"
                              format="yyyy-MM-dd" value-format="yyyy-MM-dd" prefix-icon="el-icon-date"
                              style="width: 100%"
              ></el-date-picker>
            </el-col>
          </el-row>
          <el-row :gutter="10" style="margin-top: 10px" class="row-form-list">
            <el-col :span="6" :offset="18" class="text-right">
              <el-button size="mini" type="primary" @click="getOperationRecord" round>查询</el-button>
              <el-button size="mini" @click="operationClear" round>重置</el-button>
              <el-button @click="operationLogExportDetail()" size="mini" round>导出</el-button>
            </el-col>
          </el-row>
          <div style="margin-top: 10px">
            <el-table
                :data="operationTableData"
                border
                style="width: 100%;margin-bottom: 20px;color: #000"
                :font-size="14"
                header-row-class-name	="custom-header-class-list"
            >
              <el-table-column
                  type="index"
                  width="60px"
                  align="center"
                  label="序号">
              </el-table-column>
              <el-table-column
                  label="操作账号" show-overflow-tooltip
                  align="center" width="120px">
                <template slot-scope="scope">
                  <span>{{scope.row.userName}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="操作ip" show-overflow-tooltip
                  align="center" width="150px">
                <template slot-scope="scope">
                  <span>{{scope.row.ip}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="用户类别" show-overflow-tooltip
                  align="center" width="120px">
                <template slot-scope="scope">
                  <span>{{scope.row.userType}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="所属机构" show-overflow-tooltip
                  align="center" width="250px">
                <template slot-scope="scope">
                  <span>{{scope.row.orgName}}</span>
                </template>
              </el-table-column>
<!--              <el-table-column-->
<!--                  label="所属上市公司" show-overflow-tooltip-->
<!--                  align="center" width="250px">-->
<!--                <template slot-scope="scope">-->
<!--                  <span>{{scope.row.companyName}}</span>-->
<!--                </template>-->
<!--              </el-table-column>-->
              <el-table-column
                  label="操作路径" show-overflow-tooltip
                  align="center" width="200px">
                <template slot-scope="scope">
                  <span>{{scope.row.router}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="操作内容" show-overflow-tooltip
                  align="center" width="150px">
                <template slot-scope="scope">
                  <span>{{scope.row.operationTypeDetail}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="备注" show-overflow-tooltip
                  align="center" width="300px">
                <template slot-scope="scope">
                  <span>{{scope.row.remark}}</span>
                </template>
              </el-table-column>
              <el-table-column
                  label="操作时间" show-overflow-tooltip
                  align="center" width="200px">
                <template slot-scope="scope">
                  <span>{{scope.row.createTime}}</span>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
                style="text-align: center;"
                @size-change="handleSizeChange2"
                @current-change="handleCurrentChange2"
                :current-page="operationQueryInfo.startRow"
                :page-sizes="[10, 20, 50, 200]"
                :page-size="operationQueryInfo.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="operationTotal">
            </el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import {queryPagingLogManageBySelective, exportExcel, queryLoginLogManageBySelective, loginExportExcel, queryOperationLogManageBySelective, operationLogExportExcel} from '@/api/system/log-manage-sel-api'
import {showMessage} from "stock-vue-plugin/lib/utils/message";
import {_getOrgListByType} from "@/api/person-api";

export default {

  data () {
    return {
      tabModel: 'behaviorLogs',
      loginTableData: [],
      loginTotal: 0,
      loginQueryInfo: {
        startRow: 0,
        pageSize: 10,
        operation: undefined,
        modUserId: undefined,
        modTable: undefined,
        userName: '',
        logName: '',
        logTime: '',
        formLogTime: '',
        startTime: '',
        endTime: ''
      },
      operationTableData: [],
      operationTotal: 0,
      operationQueryInfo: {
        startRow: 0,
        pageSize: 10,
        userName: '',
        orgId: '',
        companyId: '',
        startTime: '',
        endTime: ''
      },
      orgList: [],
      // companyList: [],
      tableData: [],
      total: 0,
      queryInfo: {
        startRow: 0,
        pageSize: 10,
        operation: undefined,
        modUserId: undefined,
        modTable: undefined,
        companyCode: '',
        companyName: '',
        userName: '',
        logIP: '',
        logURL: '',
        logType: '',
        methodName: '',
        createTime: '',
        startTime: '',
        endTime: ''
      },
      logType: [
        { text: "PUT", value: 'PUT' },
        { text: "POST", value: 'POST' },
        { text: "DELETE", value: 'DELETE' }
      ],
    }
  },
  mounted () {
    this.getRecord();
  },
  methods: {
    handleClick (tab) {
      if (tab.name == 'behaviorLogs') {
        this.getRecord();
        this.loginLogClear();
        this.operationClear();
      }
      if (tab.name == 'loginLogs') {
        this.getLoginRecord();
        this.operationClear();
        this.clear();
      }
      if (tab.name == 'operationLogs') {
        this.getOperationRecord();
        this.loginLogClear();
        this.getOrgList();
        this.clear();
      }
    },
    /* 登录日志 */
    handleSizeChange1(val) {
      console.log(`每页 ${val} 条`);
      this.loginQueryInfo.pageSize = val
      this.loginQueryInfo.startRow = 0
      this.getLoginRecord();
    },
    handleCurrentChange1(val) {
      console.log(`当前页: ${val}`);
      this.loginQueryInfo.startRow = val
      this.getLoginRecord();
    },
    loginLogClear () {
      this.loginQueryInfo.userName = ''
      this.loginQueryInfo.logName = ''
      this.loginQueryInfo.logTime = ''
      this.loginQueryInfo.formLogTime = ''
      this.loginQueryInfo.startTime = ''
      this.loginQueryInfo.endTime = ''
      this.loginQueryInfo.startRow = 0
      this.loginQueryInfo.pageSize = 10
      this.getLoginRecord()
    },
    loginLogExportDetail () {
      const _self = this;
      if (_self.loginQueryInfo.formLogTime != '' && _self.loginQueryInfo.formLogTime != null) {
        _self.loginQueryInfo.startTime = _self.loginQueryInfo.formLogTime[0];
        _self.loginQueryInfo.endTime = _self.loginQueryInfo.formLogTime[1];
      }
      let param = this.loginQueryInfo;
      loginExportExcel(param)
      _self.loginQueryInfo.startTime = '';
      _self.loginQueryInfo.endTime = '';
    },
    getLoginRecord () {
      const _self = this
      if (_self.loginQueryInfo.formLogTime !== '' && _self.loginQueryInfo.formLogTime != null) {
        _self.loginQueryInfo.startTime = _self.loginQueryInfo.formLogTime[0];
        _self.loginQueryInfo.endTime = _self.loginQueryInfo.formLogTime[1];
      }
      queryLoginLogManageBySelective(_self.loginQueryInfo).then(res => {
        _self.loginQueryInfo.startTime = '';
        _self.loginQueryInfo.endTime = '';
        _self.loginTableData = res.data.tableData
        _self.loginTotal = res.data.total
        if(_self.loginTableData.length === 0) {
          if (_self.loginQueryInfo.formLogTime !== '' && _self.loginQueryInfo.formLogTime != null) {
            _self.loginQueryInfo.startTime = _self.loginQueryInfo.formLogTime[0];
            _self.loginQueryInfo.endTime = _self.loginQueryInfo.formLogTime[1];
          }
          _self.loginQueryInfo.startRow = 0;
          queryLoginLogManageBySelective(_self.loginQueryInfo).then(res => {
            _self.loginQueryInfo.startTime = '';
            _self.loginQueryInfo.endTime = '';
            _self.loginTableData = res.data.tableData
            _self.loginTotal = res.data.total
          })
        }
      }).catch(error => {
        _self.loginQueryInfo.startTime = '';
        _self.loginQueryInfo.endTime = '';
      })
    },
    /* 行为日志 */
    handleSizeChange2(val) {
      console.log(`每页 ${val} 条`);
      this.operationQueryInfo.pageSize = val
      this.operationQueryInfo.startRow = 0
      this.getOperationRecord();
    },
    handleCurrentChange2(val) {
      console.log(`当前页: ${val}`);
      this.operationQueryInfo.startRow = val
      this.getOperationRecord();
    },
    operationClear () {
      this.operationQueryInfo.startRow = 0
      this.operationQueryInfo.pageSize = 10
      this.operationQueryInfo.userName = ''
      this.operationQueryInfo.orgId = ''
      this.operationQueryInfo.companyId = ''
      this.operationQueryInfo.startTime = ''
      this.operationQueryInfo.endTime = ''
      this.getOperationRecord();
    },
    operationLogExportDetail () {
      operationLogExportExcel(this.operationQueryInfo)
    },
    getOperationRecord () {
      const _self = this
      queryOperationLogManageBySelective(_self.operationQueryInfo).then(response => {
        _self.operationTableData = response.data.tableData
        _self.operationTotal = response.data.total
        if (_self.operationTableData.length === 0) {
          _self.operationQueryInfo.startRow = 0;
          queryOperationLogManageBySelective(_self.operationQueryInfo).then(response => {
            _self.operationTableData = response.data.tableData
            _self.operationTotal = response.data.total
          })
        }
      })
    },
    getOrgList() {
      _getOrgListByType({userType: '0'}).then((res) => {
        if (res.data.success) {
          this.orgList = res.data.result;
        } else {
          showMessage("error", res.data.errorMsg);
        }
      });
      // _getOrgListByType({ userType: '1' }).then((res) => {
      //   if (res.data.success) {
      //     this.companyList = res.data.result;
      //   } else {
      //     showMessage("error", res.data.errorMsg);
      //   }
      // });
    },
    /* 原行为日志废弃 */
    // getSystem (val) {
    //   if (val === undefined || val === null || val === '') {
    //     return '--';
    //   } else if (val === 'GQJL') {
    //     return '股权激励';
    //   } else if (val === 'HGGL') {
    //     return '回购管理'
    //   } else if (val === 'PLATFORM') {
    //     return '平台';
    //   } else if (val === 'TDGF') {
    //     return '合规交易';
    //   } else {
    //     return '--';
    //   }
    // },
    clear () {
      this.queryInfo.logType = '',
      this.queryInfo.companyCode = '',
      this.queryInfo.userName = '',
      this.queryInfo.logURL = '',
      this.queryInfo.startTime = '',
      this.queryInfo.endTime = ''
      this.queryInfo.startRow = 0,
      this.queryInfo.pageSize = 10,
      this.search();
    },
    exportDetail () {
      let param = this.queryInfo;
      exportExcel(param)
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.queryInfo.pageSize = val
      this.queryInfo.startRow = 0
      this.getRecord();
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
      this.queryInfo.startRow = val
      this.getRecord();
    },
    paperSearch (isSorted) {
      let form = this.$refs.paper.submitData;
      this.queryInfo.startRow = form.startRow;
      this.queryInfo.pageSize = form.pageSize;
      if (isSorted == true) {
        this.queryInfo.startRow = 0;
        this.queryInfo.pageSize = form.pageSize;
      }
      this.getRecord();
    },
    // loginLogPaperSearch (isSorted) {
    //   let form = this.$refs.paper1.submitData;
    //   this.loginQueryInfo.startRow = form.startRow;
    //   this.loginQueryInfo.pageSize = form.pageSize;
    //   if (isSorted == true) {
    //     this.loginQueryInfo.startRow = 0;
    //     this.loginQueryInfo.pageSize = form.pageSize;
    //   }
    //   this.getLoginRecord();
    // },
    getRecord () { // 查询列表数据
      const _self = this
      queryPagingLogManageBySelective(_self.queryInfo).then(response => {
        _self.tableData = response.data.tableData
        _self.total = response.data.total
        if (_self.tableData.length === 0) {
          _self.queryInfo.startRow = 0;
          queryPagingLogManageBySelective(_self.queryInfo).then(response => {
            _self.tableData = response.data.tableData
            _self.total = response.data.total
          })
        }
      }).catch(error => {
      })
    },
    search () { // 查询按钮
      let param = this.queryInfo;
      if (param.startTime && param.endTime && param.startTime > param.endTime) {
        this.$message({
          message: '请输入正确的日期范围',
          type: 'warning'
        });
        return;
      }
      this.getRecord()
    },
  }
}
</script>
<style lang="scss">
  @import '../../styles/table.css';
  .table-btn {
    border: none;
    color: #1890FF;
    padding: 0;
    width: 20px !important;
  }

  .containerUpdate {
    .el-form {
      /*border: 0px solid #eeeeee;*/
    }

    .el-row {
      margin: 0;
      height: auto !important;
    }
    .el-form .el-button--default{
      width: auto !important;
    }

  }
  .log-manage-div{
    width: 1300px;
    margin: auto;
    border-radius: 8px;
    background: #FFF;
    padding: 12px 20px 32px 20px;
    box-sizing: border-box;
  }
  .row-form-list{
    .el-input__inner {
      border-radius: 35px;
      background: #F6F7F9;
      border: 1px solid #F6F7F9;
      height: 32px;
      line-height: 32px;

    }
  }
  .custom-header-class-list{

    th{
      color: #777!important;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      background: #F6F7F9;
      padding: 10px 0px !important;

    }
  }
</style>
