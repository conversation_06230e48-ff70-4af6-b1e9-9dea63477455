<template>
  <div class="login-content" v-if="casFlag" v-loading="casFlag"></div>
  <div class="login-content" v-else>
    <el-row style="height: 100vh">

        <el-col :span="14" style="height: 100vh"  class="login-img">
              <div class="login-header">
                <div class="imgBox pd">
                  <img class="anim animated" height="32px" width="134px"
                       src="@/assets/images/logo3.png"
                  >
                  <span class="hr" style="color: #C1C1C1">|</span>
                  <span class="tits">上市公司及股东一体化服务平台</span>
                </div>
              </div>
          <img class="IMG" src="../../assets/images/img-bg.png">
        </el-col>
        <el-col :span="10" style="height: 100vh;    display: flex;
    align-items: center;
    justify-content: center;">
        <div class="login-body-box">
          <div style="text-align: center;margin-bottom: 48px">
            <img class="anim animated" width="100px" height="100px"
                 src="@/assets/images/601066.png">
            <div class="hygl">欢迎登录</div>
          </div>
          <div>
            <el-form
                :model="form"
                :rules="rules"
                class="m-t-20 login-form"
                id="formLogin"
                ref="formLogin">
              <el-form-item prop="username" style="margin-bottom:32px;">
                <el-input
                    placeholder="请输入用户名"
                    type="text"
                    v-model="form.username"
                    @input="handleUserNameInput">
                  <i slot="prefix" class="iconfont  icon-ic-user-line"></i>
                </el-input>
              </el-form-item>
              <el-form-item prop="password" style="margin-bottom:32px;">
                <el-input
                    autocomplete="true"
                    placeholder="请输入密码"
                    type="password"
                    show-password
                    v-model="form.password"
                    @input="handlePasswordInput"
                >
                  <i slot="prefix" class="iconfont  icon-ic-lock-o"></i>
                </el-input>
              </el-form-item>
              <el-form-item prop="verifyCode" style="margin-bottom: 24px">
                <el-row>
                  <el-col span="14">
                    <el-input
                        :placeholder="sendCodeButtonNameHolder"
                        type="text"
                        v-model="form.verifyCode"
                        @input="handleVerifyCodeInput"
                    >
                      <i slot="prefix" class="iconfont  icon-ic-email-o"></i>
                    </el-input>
                  </el-col>
                  <el-col span="10" style="text-align: right">
                    <el-button
                        @click="handleShowVerify"
                        round
                        type="primary"
                        :disabled="sendCodeButtonName1 !== '发送验证码' && sendCodeButtonName1 !== '重新获取'"
                    >{{ sendCodeButtonName1 }}
                    </el-button>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-row>
                <el-col align="left" span="12">
                  <el-checkbox v-model="form.rememberMe"><span style="color: #777;">记住密码</span></el-checkbox>
                </el-col>
                <el-col align="right" span="12">
                  <span class="clickable" @click="showForgotPassword" style="color: #CF1A1C;font-size: 14px;">忘记密码?</span>
                </el-col>
              </el-row>
              <el-form-item style="margin-top:64px;margin-bottom: 10px !important;">
                <el-button @click="handleSubmit" class="full" type="primary" round>登 录</el-button>
              </el-form-item>
              <el-row>
                <el-col align="center" span="24">
                  <el-checkbox v-model="form.readAgree"></el-checkbox>
                  <span style="font-size: 12px">
                    我已阅读并同意&nbsp;
                    <span class="agreement-box" @click="goAgreement('1')">用户协议</span>&nbsp;和&nbsp;
                    <span class="agreement-box" @click="goAgreement('2')">隐私政策</span>
                  </span>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog
        title="修改密码"
        :visible.sync="forgotPasswordFlag"
        v-if="forgotPasswordFlag"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="600px">
      <el-form :model="forgotPasswordForm" :rules="forgotPasswordRules" label-width="110px" ref="forgotPasswordForm">
        <el-form-item label="用户名" prop="userName">
          <el-input placeholder="请输入用户名" v-model="forgotPasswordForm.userName"></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input placeholder="请输入新密码" v-model="forgotPasswordForm.newPassword" type="password" :show-password="true"></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="repeatNewPassword">
          <el-input placeholder="请确认新密码" v-model="forgotPasswordForm.repeatNewPassword" type="password" :show-password="true"></el-input>
        </el-form-item>
        <el-form-item label="验证码" prop="captcha">
          <el-row>
            <el-col span="14">
              <el-input placeholder="请输入邮箱验证码" type="text" v-model="forgotPasswordForm.captcha" @input="handleVerifyCodeInput"></el-input>
            </el-col>
            <el-col span="10" style="text-align: right">
              <el-button round @click="getMailCaptcha" :disabled="sendCodeButtonName !== '发送邮箱验证码' && sendCodeButtonName !== '重新获取'"
                         style="color: #CF1A1C;border-color: #CF1A1C;">{{ sendCodeButtonName }}</el-button>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="closeForgotPassword('0')" round>取消</el-button>
        <el-button @click="closeForgotPassword('1')" round type="primary">保存</el-button>
      </div>
    </el-dialog>
    <!-- 文字点选验证码 -->
    <Verify
        ref="verify"
        :captcha-type="'clickWord'"
        :img-size="{width:'400px',height:'200px'}"
        @success="handleGetVerifyCode"
    />
    <el-dialog :visible.sync="showAgreement" width="730px" v-if="showAgreement" custom-class="agreement-dialog"
               :close-on-click-modal="false"  :close-on-press-escape="false" :append-to-body="true" :show-close="false"
    >
      <div class="temp-div">
        <img style="position: absolute;right: 0;bottom: 0;pointer-events: none" :src="dbUrl" width="276px" alt="">
        <div class="text-center" style="font-size:18px;font-weight:600;margin-bottom: 24px;color: #333333;">
          {{agreementTitle}}
        </div>
        <div style="max-height:440px;overflow-y: auto;" v-html="agreementContent"></div>
        <div class="" slot="footer" style="text-align:center;margin-top: 24px;">
          <el-button size="small" type="primary" @click="handleCancel"
                     class="relief-modal-button">我已知晓并同意遵守
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { mapActions } from 'vuex'
import {
  _getVerifyCode,
  _login,
  _captchaCodeOnChange,
  _getUserNameVerifyCode,
  _submitTicketAndService,
  _getMailCaptcha,
  _saveNewPassword,
  _getDynamicCodeByUsername,
  checkUser
} from "@/api/user-api";
import { showMessage } from "stock-vue-plugin/lib/utils/message";
import {_validatePassword, _validatePhone, _validateVerifyCode} from "@/utils/validate";
import md5 from 'blueimp-md5';
import Verify from '@/components/verifition/Verify'
import { _getAgreement, _saveLoginAgreementLog } from "@/api/agreement-api";

// 引入base64
const Base64 = require("js-base64").Base64;

export default {
  components: {
    Verify
  },
  data () {
    let validatePhone = (rule, value, cb) => {
      if (_validatePhone(value)) {
        return cb();
      }
      cb(new Error("手机号格式错误"))
    };
    let validateVerifyCode = (rule, value, cb) => {
      if (_validateVerifyCode(value)) {
        return cb();
      }
      cb(new Error("验证码格式错误"))
    };
    let validatePassword = (rule,value, callback) => {
      if (!_validatePassword(value)) {
        callback(new Error("密码不符合规则：【长度8-32位，需要包含数字、大小写字母、特殊符号】"))
      }else {
        callback();
      }
    };
    let validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.forgotPasswordForm.newPassword) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    };
    return {
      casFlag: true,
      sendCodeButtonNameHolder:'请输入短信验证码',
      // 文字验证码
      captchaTextCode: null,
      forgotPasswordFlag: false,
      forgotPasswordForm: {
        userName: '',
        newPassword: '',
        repeatNewPassword: '',
        captcha: '',
        password: ''
      },
      userNameSendCodeButtonName: '发送验证码',
      userNameTimes: 60,
      sendCodeButtonName: '发送邮箱验证码',
      sendCodeButtonName1: '发送验证码',
      captchaCodeKey: true,
      times: 60,
      times1: 60,
      activeNames: 'passwordLogin',
      form: {
        username: '',
        password: '',
        telephone: '',
        verifyCode: '',
        loginType: '0',
        rememberMe: false,
        captchaCode: '',
        captchaCode1: '',
        readAgree: false
      },
      hiddenKey: '0',
      showAgreement: false,
      dbUrl: require("../../assets/images/image-db.png"),
      agreementTitle: '',
      agreementData: [],
      agreementContent: '',
      rules: {
        username: [
          { required: true, message: '请输入正确的用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        telephone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { validator: validatePhone }
        ],
        verifyCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { validator: validateVerifyCode }
        ],
        captchaCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' }
        ],
        captchaCode1: [
          { required: true, message: '请输入验证码', trigger: 'blur' }
        ]
      },
      forgotPasswordRules: {
        userName: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validatePassword, trigger: 'blur' }
        ],
        repeatNewPassword: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          { validator: validatePass, trigger: 'blur' }
        ],
        captcha: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
          { min: 6, message: '验证码必须是6位', trigger: 'blur' },
          { max: 6, message: '验证码必须是6位', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    this.casLogin()
    this.getAgreement()
  },
  methods: {
    casLogin() {
      const LoginTypeIsCas = new URLSearchParams(window.location.search).get('LoginTypeIsCas');
      const ticket = new URLSearchParams(window.location.search).get('ticket');

      if ((LoginTypeIsCas && LoginTypeIsCas !== '') || (ticket && ticket !== '')) {

        let url = encodeURIComponent(window.location.origin + "/ui/platform/user/login");
        let baseUrl = process.env.VUE_APP_CASURL + "/login?service=" + url;

        if (ticket && ticket !== '') {
          let DNS = window.location.origin
          if (DNS.includes('esop.csc108.com')) {
            showMessage('error', "内部人员请从内网环境登录");
            setTimeout(function() {
              let url = encodeURIComponent(window.location.origin + "/ui/platform/user/login");
              let baseUrl =  process.env.VUE_APP_CASURL + "/logout?service=" + process.env.VUE_APP_CASURL + "/login?service=" + url
              window.open(baseUrl, "_self");
            }, 2000);
          } else {
            let param = {
              ticket: ticket,
              service: url
            }
            this.submitTicketAndService(param)
          }
        } else {
          window.open(baseUrl, "_self");
        }
      } else {
        this.casFlag = false
      }
    },
    getAgreement () {
      _getAgreement().then(res => {
        if (res.data.success) {
          this.agreementData = res.data.result
        }
      })
    },
    submitTicketAndService(param) {
      _submitTicketAndService(param).then(res => {
        if (!res.data.success) {
          showMessage('error', res.data.errorMsg)
          setTimeout(function() {
            let url = encodeURIComponent(window.location.origin + "/ui/platform/user/login");
            let baseUrl =  process.env.VUE_APP_CASURL + "/logout?service=" + process.env.VUE_APP_CASURL + "/login?service=" + url
            window.open(baseUrl, "_self");
          }, 2000);
        }
      }).catch(err => {
        let url = encodeURIComponent(window.location.origin + "/ui/platform/user/login");
        let baseUrl = process.env.VUE_APP_CASURL + "/logout?service=" + url
        window.open(baseUrl, "_self");
        // window.open(baseUrl, "_self");
      })
    },
    ...mapActions(['Login', 'Logout']),

    // 打开文字点击验证码弹窗
    handleShowVerify () {
      // 判断用户名和密码是否填写
      if (!this.form.username){
        showMessage('error', "请输入用户名");
        return;
      }else if (!this.form.password){
        showMessage('error', "请输入密码");
        return;
      }
      this.$refs.verify.show()
    },
    handleSubmit () {

      if (this.form.loginType === '0') {
        this.$refs['formLogin'].validate(valid => {
          if (valid) {
            if (!this.form.readAgree) {
              this.goAgreement("2")
              return;
            }
            let data = {
              username: this.form.username,
              password: md5(this.form.password),
              telephone: this.form.telephone,
              verifyCode: this.form.verifyCode,
              loginType: '0',
              rememberMe: this.form.rememberMe,
              captchaCode: this.form.captchaCode,
              // 文字点击验证码二次验证参数
              captchaTextCode: this.captchaTextCode && encodeURIComponent(this.captchaTextCode.captchaVerification)
            }
            // 内部用户不能从登录页登录系统,只能cas登录
            checkUser({userName: this.form.username}).then(res => {
              if (res.data.result) {
                _login(data).then(response => {
                  if (!response.data.success) {
                    showMessage('error', response.data.errorMsg)
                    this.$refs.captcha_img.onclick();
                    this.form.captchaCode = '';
                    // 刷新文字点击验证码
                    this.$refs.verify.refresh()
                  } else {
                    // 记录登录协议日志
                   _saveLoginAgreementLog(this.agreementData)
                  }
                  if (this.form.rememberMe) {
                    let jsonObj = {}
                    jsonObj.username = this.form.username
                    jsonObj.password = Base64.encode(this.form.password)
                    let obj = JSON.stringify(jsonObj)
                    // 如果选择记住密码，将账号和密码存缓存
                    window.localStorage.setItem("temp", Base64.encode(obj))
                  } else {
                    // 不选择密码 将以前的存储密码清除掉
                    window.localStorage.setItem("temp", "")
                  }
                })
              } else {
                showMessage('error', "当前账号为内部用户使用账号,请从cas登录")
              }
            })
          } else {
            showMessage('error', "请完善登录信息")
          }
        })
      } else if (this.form.loginType === '1') {
        this.$refs['codeFormLogin'].validate(valid => {
          if (valid) {
            _login(this.form).then(response => {
              if (!response.data.success) {
                showMessage('error', response.data.errorMsg)
              }
            })
          } else {
            showMessage('error', "请完善验证码登录信息")
          }
        })
      }
    },
    handleUserNameGetVerifyCode () {
      let param = { telephone: this.form.telephone };
      _getUserNameVerifyCode(param).then(response => {
        if (!response.data.success) {
          showMessage('error', response.data.errorMsg)
        } else {
          showMessage('info', '验证码已发送')
          this.userNameTimer = setInterval(() => {
            this.userNameSendCodeButtonName = (this.userNameTimes--) + 's';
            if (this.userNameTimes === 0) {
              this.userNameSendCodeButtonName = '重新获取'
              // 重新置回60秒
              this.userNameTimes = 60;
              clearInterval(this.userNameTimer);
            }
          }, 1000)
        }
      });
    },
    // 发送手机验证码
    handleGetVerifyCode (e) {
      this.captchaTextCode = e;
      // let param = {
      //   username: this.form.username,
      //   password: md5(this.form.password)
      // }
      // _getVerifyCode(param).then(response => {
      //   if (!response.data.success) {
      //     showMessage('error', response.data.errorMsg)
      //   } else {
      //     this.timer1 = setInterval(() => {
      //       this.sendCodeButtonName1 = '重新获取' + (this.times1--) + 's';
      //       if (this.times1 === 0) {
      //         this.sendCodeButtonName1 = '重新获取'
      //         // 重新置回60秒
      //         this.times1 = 60;
      //         clearInterval(this.timer1)
      //       }
      //     }, 1000);
      //     this.sendCodeButtonNameHolder = "已发送至" + response.data.result;
      //     showMessage('info', '验证码已发送')
      //   }
      // });

    },
    handleTelephoneInput () {
      this.form.telephone = this.form.telephone.replace(/[^\d]/g, '')
      if (this.form.telephone.length > 11) {
        this.form.telephone = this.form.telephone.substring(0, 11);
      }
    },
    handleVerifyCodeInput () {
      this.form.verifyCode = this.form.verifyCode.replace(/[^\d]/g, '')
      if (this.form.verifyCode.length > 6) {
        this.form.verifyCode = this.form.verifyCode.substring(0, 6);
      }
    },
    handleUserNameInput () {
      let str = localStorage.getItem("temp");
      if (str !== '' && str != null) {
        let formObject = JSON.parse(Base64.decode(str))
        if (formObject != null && this.form.username === formObject.username) {
          this.form.password = Base64.decode(formObject.password)
          this.form.rememberMe = true
        }
      }
      if (this.form.username.length > 23) {
        this.form.username = this.form.username.substring(0, 23);
      }
    },
    handlePasswordInput () {
      if (this.form.password.length > 20) {
        this.form.password = this.form.password.substring(0, 20);
      }
    },
    // 图形验证码校验,校验通过才能发送短信验证码
    captchaCodeOnChange () {
      let param = {
        captchaCode: this.form.captchaCode1
      }
      _captchaCodeOnChange(param).then(response => {
        if (response.data.result) {
          this.captchaCodeKey = false;
        } else {
          this.captchaCodeKey = true;
        }
      });
    },
    showForgotPassword() {
      this.forgotPasswordForm.userName = this.form.username
      this.forgotPasswordForm.newPassword = ''
      this.forgotPasswordForm.repeatNewPassword = ''
      this.forgotPasswordForm.captcha = ''
      this.forgotPasswordFlag = true
    },
    matchPassword(rule, value, callback) {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.forgotPasswordForm.newPassword) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    },
    closeForgotPassword(flag) {
      if (flag === '1') {
        this.$refs.forgotPasswordForm.validate((valid) => {
          if (valid) {
            this.forgotPasswordForm.password = this.forgotPasswordForm.newPassword
            this.forgotPasswordForm.newPassword = md5(this.forgotPasswordForm.newPassword)
            this.forgotPasswordForm.repeatNewPassword = md5(this.forgotPasswordForm.repeatNewPassword)
            // 请求后台判断用户名是否正确,判断验证码是否正确
            _saveNewPassword(this.forgotPasswordForm).then(res => {
              if (res.data.result) {
                showMessage('info', '密码修改成功')
                this.forgotPasswordFlag = false
              } else {
                showMessage('info', res.data.errorMsg)
                this.forgotPasswordForm.newPassword = this.forgotPasswordForm.password
                this.forgotPasswordForm.repeatNewPassword = this.forgotPasswordForm.password
              }
            })
          }
        })
      } else {
        this.forgotPasswordFlag = false
      }
    },
    getMailCaptcha () {
      if (this.forgotPasswordForm.userName !== '') {
        // 检验用户名
        let param = {
          userName: this.forgotPasswordForm.userName
        }
        _getMailCaptcha(param).then(res =>{
          if (res.data.result) {
            showMessage('info', '验证码已发送')
            this.timer = setInterval(() => {
              this.sendCodeButtonName = (this.times--) + 's';
              if (this.times === 0) {
                this.sendCodeButtonName = '重新获取'
                // 重新置回60秒
                this.times = 60;
                clearInterval(this.timer)
              }
            }, 1000)
          } else {
            showMessage('error', res.data.errorMsg)
          }
        })
      } else {
        this.$refs['forgotPasswordForm'].validateField('userName')
      }
    },
    adminActivate(status) {
      this.hiddenKey = status
      showMessage('info', "admin账号激活成功")
    },
    goAgreement (param) {
      if (param === '1') {
        this.agreementTitle = '用户协议'
        this.agreementContent = this.agreementData.find(em => em.agreementType === '1').agreementContent
      } else {
        this.agreementTitle = '隐私政策声明'
        this.agreementContent = this.agreementData.find(em => em.agreementType === '2').agreementContent
      }
      this.showAgreement = true
    },
    handleCancel () {
      this.form.readAgree = true
      this.showAgreement = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import './login.scss';

/*.login-content{
  position: relative;
}*/
.Copyright {
  /* position: fixed;
   top: calc(50% + 228px);
   right: calc(50% - 100px);
   font-size: 14px;
   font-weight: 400;
   color: #999999;*/
  line-height: 20px;
  position: absolute;
  bottom: 20px;
  color: #ffffff;
  left: calc(57% - 150px);
}

.user-layout-login {
  label {
    font-size: 14px;
  }

  .getCaptcha {
    display: block;
    width: 100%;
    height: 40px;
  }

  .forge-password {
    font-size: 14px;
  }

  button.login-button {
    padding: 0 15px;
    font-size: 16px;
    height: 40px;
    width: 100%;
  }

  .user-login-other {
    text-align: left;
    margin-top: 24px;
    line-height: 22px;

    .item-icon {
      font-size: 24px;
      color: rgba(0, 0, 0, 0.2);
      margin-left: 16px;
      vertical-align: middle;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: $--el-color-primary;
      }
    }

    .register {
      float: right;
    }
  }
}
.agreement-box {
  color: blue;
  cursor: pointer;
}
.temp-div{
  background: linear-gradient(180deg, #FFF0F0 0%, #FFFFFF 54.48%);
  padding: 24px;
  line-height: 23px;
}
::v-deep .agreement-dialog .el-dialog__body {
  padding: 0 !important;
  border-radius: 16px !important;
}
::v-deep .agreement-dialog .el-dialog__header {
  padding: 0 !important;
}
::v-deep .agreement-dialog .el-dialog__header {
  display: none !important;
}
::v-deep .agreement-dialog {
  border-radius: 16px !important;
}
</style>

<style>
.login-body-box .el-tabs__nav {
  width: 100% !important;

}

.login-body-box .el-tabs__item {
  width: 50% !important;
  text-align: center;
}

.clickable:hover {
  /* 这里定义悬浮时的样式 */
  cursor: pointer;
}
.login-form .el-input__inner{
  border-radius: 34px;
  background: #FFF;
}

</style>
